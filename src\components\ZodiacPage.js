import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import SmokeAnimation from './SmokeAnimation';
import KuberaAnimation from './KuberaAnimation';
import LanguageSelector from './LanguageSelector';
import TranslationLoader from './TranslationLoader';
import HoroscopeService from '../services/HoroscopeService';
import { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';
import { useTranslatedContent, useUITranslations } from '../hooks/useTranslatedContent';


const zodiacIcons = {
  aries: '♈',
  taurus: '♉',
  gemini: '♊',
  cancer: '♋',
  leo: '♌',
  virgo: '♍',
  libra: '♎',
  scorpio: '♏',
  sagittarius: '♐',
  capricorn: '♑',
  aquarius: '♒',
  pisces: '♓'
};

// Advanced horoscope parser that extracts structured content
const parseHoroscopeIntoStructuredCategories = (rawText) => {
  // Check if rawText is valid
  if (!rawText || typeof rawText !== 'string') {
    return [];
  }
  
  // Clean the raw text first
  const cleanText = rawText
    .replace(/\*\*/g, '')
    .replace(/##/g, '')
    .replace(/\*/g, '')
    .replace(/\[.*?\]/g, '')
    .trim();

  const categories = {
    love: {
      id: 'love',
      title: 'ආදරය සහ සම්බන්ධතා',
      emoji: '💕',
      icon: '❤️',
      content: '',
      keywords: ['ආදර', 'සම්බන්ධතා', 'ප්‍රේම', 'විවාහ', 'මිත්‍ර']
    },
    career: {
      id: 'career',
      title: 'වෘත්තීය ජීවිතය',
      emoji: '💼',
      icon: '🏢',
      content: '',
      keywords: ['වෘත්ති', 'කාර්', 'රැකියා', 'ව්‍යාපාර', 'සේවා']
    },
    health: {
      id: 'health',
      title: 'සෞඛ්‍ය සහ යහපැවැත්ම',
      emoji: '🌿',
      icon: '🏥',
      content: '',
      keywords: ['සෞඛ්', 'සෞඛ', 'යහපැවැත්ම', 'ශරීර', 'මානසික']
    },
    finance: {
      id: 'finance',
      title: 'මූල්‍ය කටයුතු',
      emoji: '💰',
      icon: '💳',
      content: '',
      keywords: ['මූල්', 'මුදල්', 'ආර්ථික', 'ආදායම', 'වියදම']
    },
    general: {
      id: 'general',
      title: 'සාමාන්‍ය උපදෙස්',
      emoji: '✨',
      icon: '🔮',
      content: '',
      keywords: ['සාමාන්', 'උපදෙස්', 'සාර්ථක', 'ජීවිත', 'දිනය']
    }
  };

  // Split text into lines and process
  const lines = cleanText.split('\n').filter(line => line.trim().length > 0);
  let currentCategory = null;
  let contentBuffer = [];
  
  // If no clear structure, distribute content evenly across categories
  if (lines.length < 5) {
    // Short content - put everything in general
    categories.general.content = cleanText;
  } else {
    // Process each line to categorize content
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (!line || line.length < 2) continue;
      
      // Detect category by keywords or numbered sections
      let detectedCategory = null;
      
      // Check for numbered sections (1., 2., 3., etc.)
      const numberedMatch = line.match(/^(\d+)\./); 
      if (numberedMatch) {
        const num = parseInt(numberedMatch[1]);
        const categoryOrder = ['love', 'career', 'health', 'finance', 'general'];
        if (num >= 1 && num <= 5) {
          detectedCategory = categoryOrder[num - 1];
        }
      }
      
      // Check for keyword-based detection (more flexible)
      if (!detectedCategory) {
        for (const [catId, catData] of Object.entries(categories)) {
          for (const keyword of catData.keywords) {
            if (line.toLowerCase().includes(keyword.toLowerCase())) {
              detectedCategory = catId;
              break;
            }
          }
          if (detectedCategory) break;
        }
      }
      
      // If we found a new category, save previous content
      if (detectedCategory && detectedCategory !== currentCategory) {
        if (currentCategory && contentBuffer.length > 0) {
          categories[currentCategory].content = contentBuffer.join(' ').trim();
        }
        currentCategory = detectedCategory;
        contentBuffer = [];
        
        // Clean the line and add to buffer
        let cleanContent = line
          .replace(/^\d+\.\s*/, '')
          .replace(/^[•-]\s*/, '')
          .replace(new RegExp(categories[detectedCategory].title, 'gi'), '')
          .replace(/:/g, '')
          .trim();
        
        if (cleanContent.length > 0) {
          contentBuffer.push(cleanContent);
        }
      } else if (currentCategory) {
        // Add content to current category
        let cleanContent = line.trim();
        if (cleanContent.length > 0) {
          contentBuffer.push(cleanContent);
        }
      } else {
        // No category detected yet, start with general and add content
        currentCategory = 'general';
        contentBuffer.push(line.trim());
       }
    }
    
    // If no categories were detected, distribute content intelligently
    if (!Object.values(categories).some(cat => cat.content)) {
      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 10);
      const categoriesArray = Object.keys(categories);
      
      sentences.forEach((sentence, index) => {
        const categoryIndex = index % categoriesArray.length;
        const categoryKey = categoriesArray[categoryIndex];
        if (!categories[categoryKey].content) {
          categories[categoryKey].content = sentence.trim();
        } else {
          categories[categoryKey].content += ' ' + sentence.trim();
        }
      });
    }
   }
   
   // Save final category content
   if (currentCategory && contentBuffer.length > 0) {
     categories[currentCategory].content = contentBuffer.join(' ').trim();
   }
   
   // Ensure all categories have meaningful content
  Object.values(categories).forEach((category, index) => {
    if (!category.content || category.content.length < 5) {
      // If still no content, use a portion of the original text
      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 5);
      if (sentences.length > index) {
        category.content = sentences[index].trim() || cleanText.substring(index * 50, (index + 1) * 50).trim();
      } else {
        // Last resort - use generic content based on category
        const genericContent = {
          love: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',
          career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',
          health: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',
          finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',
          general: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'
        };
        category.content = genericContent[category.id] || 'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';
      }
    }
  });
   
   // Ensure each category has its id properly set and return as array
   return Object.entries(categories).map(([key, category]) => ({
     ...category,
     id: key // Ensure id is properly set
   }));
 };
 
 // Beautiful category card component
 const CategoryCard = ({ category, index, translatedTitle, translatedContent }) => {
   const cardStyles = {
     love: {
       background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',
       border: '1px solid rgba(255, 182, 193, 0.3)',
       shadow: '0 8px 32px rgba(255, 105, 180, 0.1)'
     },
     career: {
       background: 'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',
       border: '1px solid rgba(70, 130, 180, 0.3)',
       shadow: '0 8px 32px rgba(30, 144, 255, 0.1)'
     },
     health: {
       background: 'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',
       border: '1px solid rgba(144, 238, 144, 0.3)',
       shadow: '0 8px 32px rgba(50, 205, 50, 0.1)'
     },
     finance: {
       background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',
       border: '1px solid rgba(255, 215, 0, 0.3)',
       shadow: '0 8px 32px rgba(255, 165, 0, 0.1)'
     },
     general: {
       background: 'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',
       border: '1px solid rgba(221, 160, 221, 0.3)',
       shadow: '0 8px 32px rgba(147, 112, 219, 0.1)'
     }
   };
   
   const style = cardStyles[category.id] || cardStyles.general;
   
   return (
     <div 
       className="horoscope-category-card"
       style={{
         marginBottom: '2rem',
         padding: '2rem',
         background: style.background,
         border: style.border,
         borderRadius: '20px',
         boxShadow: style.shadow,
         backdropFilter: 'blur(10px)',
         transition: 'all 0.3s ease',
         position: 'relative',
         overflow: 'hidden'
       }}
       onMouseEnter={(e) => {
         e.currentTarget.style.transform = 'translateY(-5px)';
         e.currentTarget.style.boxShadow = style.shadow.replace('0.1)', '0.2)');
       }}
       onMouseLeave={(e) => {
         e.currentTarget.style.transform = 'translateY(0)';
         e.currentTarget.style.boxShadow = style.shadow;
       }}
     >
       {/* Decorative background pattern */}
       <div 
         style={{
           position: 'absolute',
           top: '-50%',
           right: '-50%',
           width: '200%',
           height: '200%',
           background: `radial-gradient(circle, ${style.border.replace('1px solid ', '').replace('0.3)', '0.05)')} 1px, transparent 1px)`,
           backgroundSize: '20px 20px',
           opacity: 0.3,
           pointerEvents: 'none'
         }}
       />
       
       {/* Header */}
       <div 
         style={{
           display: 'flex',
           alignItems: 'center',
           marginBottom: '1.5rem',
           position: 'relative',
           zIndex: 1
         }}
       >
         <div 
           style={{
             fontSize: '2.5rem',
             marginRight: '1rem',
             filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
           }}
         >
           {category.emoji}
         </div>
         <div>
           <h3 
             style={{
               color: '#f4d03f',
               fontSize: '1.4rem',
               margin: 0,
               fontFamily: 'Noto Sans Sinhala, sans-serif',
               fontWeight: '600',
               textShadow: '0 2px 4px rgba(0,0,0,0.1)'
             }}
           >
             {translatedTitle || category.title}
           </h3>
           <div 
             style={{
               width: '50px',
               height: '3px',
               background: 'linear-gradient(90deg, #f4d03f, transparent)',
               marginTop: '0.5rem',
               borderRadius: '2px'
             }}
           />
         </div>
       </div>
       
       {/* Content */}
       <div 
         style={{
           position: 'relative',
           zIndex: 1
         }}
       >
         <p 
           style={{
             color: '#e8f4fd',
             lineHeight: '1.8',
             fontSize: '1.1rem',
             margin: 0,
             fontFamily: 'Noto Sans Sinhala, sans-serif',
             textAlign: 'justify',
             textShadow: '0 1px 2px rgba(0,0,0,0.1)'
           }}
         >
           {translatedContent || category.content}
         </p>
       </div>
       
       {/* Bottom accent */}
       <div 
         style={{
           position: 'absolute',
           bottom: 0,
           left: 0,
           right: 0,
           height: '4px',
           background: `linear-gradient(90deg, ${style.border.replace('1px solid ', '').replace('0.3)', '0.6)')}, transparent)`,
           borderRadius: '0 0 20px 20px'
         }}
       />
     </div>
   );
 };
 
 // Main display component
const StructuredHoroscopeDisplay = ({ horoscope }) => {
  const { getTranslatedCategories, isLoading: translationLoading } = useTranslatedContent();
  const [translatedCategories, setTranslatedCategories] = useState(null);
  const { getUIText } = useUITranslations();
  let categories;

  // Translate categories when horoscope or language changes
  useEffect(() => {
    const translateCategories = async () => {
      if (categories && categories.length > 0) {
        const translated = await getTranslatedCategories(categories);
        setTranslatedCategories(translated);
      }
    };

    translateCategories();
  }, [horoscope, getTranslatedCategories]);

  // Check if we have structured data from the new API
  if (horoscope && horoscope.structured && horoscope.categories) {
    // Handle both object and array formats
    if (Array.isArray(horoscope.categories)) {
      categories = horoscope.categories;
    } else {
      // Convert object to array with proper ids
      categories = Object.entries(horoscope.categories).map(([key, category]) => ({
        ...category,
        id: category.id || key
      }));
    }
  } else if (typeof horoscope === 'string') {
    // Fallback to parsing raw text
    categories = parseHoroscopeIntoStructuredCategories(horoscope);
  } else {
    categories = [];
  }
  
  // Fallback if no categories found
  if (!categories || categories.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '2rem',
        color: '#e8f4fd',
        fontFamily: 'Noto Sans Sinhala, sans-serif'
      }}>
        {getUIText('loading')}
      </div>
    );
  }
  
  return (
    <div 
      className="structured-horoscope-display"
      style={{
        maxWidth: '800px',
        margin: '0 auto',
        padding: '1rem'
      }}
    >
      {(translatedCategories || categories).map((category, index) => {
        const originalCategory = categories[index];
        return (
          <CategoryCard
            key={category.id || `category-${index}`}
            category={originalCategory}
            index={index}
            translatedTitle={category.title}
            translatedContent={category.content}
          />
        );
      })}
    </div>
  );
};

const ZodiacPage = ({ sign }) => {
  const [horoscope, setHoroscope] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [lastUpdated, setLastUpdated] = useState(null);
  const { getTranslatedText } = useTranslatedContent();
  const { getUIText } = useUITranslations();
  const [translatedSignName, setTranslatedSignName] = useState('');

  // Analytics integration
  const analytics = useAnalytics();
  useComponentTracking('ZodiacPage');
  const [refreshing, setRefreshing] = useState(false);

  const fetchHoroscope = useCallback(async (forceRefresh = false) => {
    try {
      // Track zodiac page view
      analytics.trackZodiacView(sign.id);

      if (forceRefresh) {
        setRefreshing(true);
        analytics.trackEvent('horoscope_refresh', {
          event_category: 'user_action',
          zodiac_sign: sign.id
        });
      } else {
        setLoading(true);
      }
      setError('');
      
      // Check cache first (skip cache if force refresh)
      if (!forceRefresh) {
        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);
        if (cachedHoroscope) {
          setHoroscope(cachedHoroscope);
          setLastUpdated(new Date());
          setLoading(false);
          return;
        }
      }

      // Fetch structured horoscope data from the new API
      const horoscopeData = await HoroscopeService.getHoroscope(sign.id, forceRefresh);
      
      // Check if we got structured data
      if (horoscopeData && horoscopeData.categories) {
        // Convert API response to the format expected by the display component
        const categoryConfig = {
          love: { id: 'love', title: 'ආදරය සහ සම්බන්ධතා', emoji: '💕' },
          career: { id: 'career', title: 'වෘත්තීය ජීවිතය', emoji: '💼' },
          health: { id: 'health', title: 'සෞඛ්‍ය සහ යහපැවැත්ම', emoji: '🌿' },
          finance: { id: 'finance', title: 'මූල්‍ය කටයුතු', emoji: '💰' },
          general: { id: 'general', title: 'සාමාන්‍ය උපදෙස්', emoji: '✨' }
        };
        
        const categories = Object.entries(horoscopeData.categories).map(([key, content]) => ({
          ...categoryConfig[key],
          content: content || 'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'
        }));
        
        setHoroscope({ 
          categories, 
          structured: true,
          dateCreated: horoscopeData.date_created,
          createdAt: horoscopeData.created_at,
          rawContent: horoscopeData.raw_content
        });
      } else {
        // Fallback to old parsing method if we get raw text
        setHoroscope(horoscopeData);
      }
      
      setLastUpdated(new Date());

      // Track successful horoscope load
      analytics.trackEvent('horoscope_loaded', {
        event_category: 'content_interaction',
        zodiac_sign: sign.id,
        load_type: forceRefresh ? 'refresh' : 'initial',
        content_length: typeof horoscopeData === 'string' ? horoscopeData.length : JSON.stringify(horoscopeData).length
      });

      // Cache the result
      HoroscopeService.setCachedHoroscope(sign.id, horoscopeData);
      
    } catch (err) {
      setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');
      console.error('Error fetching horoscope:', err);

      // Track error
      analytics.trackError(
        `Horoscope fetch error: ${err.message}`,
        `ZodiacPage-${sign.id}`,
        false
      );
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [sign.id]);

  useEffect(() => {
    fetchHoroscope();
  }, [fetchHoroscope]);



  // Translate zodiac sign name when language changes
  useEffect(() => {
    const translateSignName = async () => {
      const translated = await getTranslatedText(sign.sinhala, 'zodiac_sign_name');
      setTranslatedSignName(translated);
    };

    translateSignName();
  }, [sign.sinhala, getTranslatedText]);

  const handleRefresh = () => {
    fetchHoroscope(true);
  };





  const getCurrentDate = () => {
    const today = new Date();
    const options = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      weekday: 'long'
    };
    return today.toLocaleDateString('si-LK', options);
  };

  return (
    <TranslationLoader>
      <div className="zodiac-page">

      
      {/* Divine Background Image */}
      <div className="divine-background">
        <img 
          src={`/god.jpg?v=${window.CACHE_VERSION || Date.now()}`}
          alt="Divine Blessing" 
          className="god-image"
        />
      </div>
      
      <ParticleBackground />
      <SmokeAnimation />
      <KuberaAnimation />
      
      <Link to="/" className="back-button">
        {getUIText('backToHome')}
      </Link>

      {/* Language Selector */}
      <div className="language-selector-container" style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 1001
      }}>
        <LanguageSelector />
      </div>

      <div className="zodiac-content">
        <div className="zodiac-header">
          <div className="zodiac-icon" style={{ fontSize: '5rem', marginBottom: '1rem' }}>
            {zodiacIcons[sign.id]}
          </div>
          <h1 className="zodiac-title">{translatedSignName || sign.sinhala}</h1>
          <h2 className="zodiac-subtitle">{sign.english} {getUIText('zodiacSign')}</h2>
          
          {/* Display horoscope date if available */}
          {horoscope && horoscope.dateCreated ? (
            <div style={{ 
              background: 'rgba(244, 208, 63, 0.15)',
              border: '1px solid rgba(244, 208, 63, 0.3)',
              borderRadius: '15px',
              padding: '1rem',
              marginBottom: '2rem',
              textAlign: 'center'
            }}>
              <div style={{ color: '#f4d03f', fontSize: '1.1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                {getUIText('horoscopeDate')}
              </div>
              <div style={{ color: '#ffffff', fontSize: '1rem' }}>
                {new Date(horoscope.dateCreated).toLocaleDateString('si-LK', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  weekday: 'long'
                })}
              </div>
              {horoscope.createdAt && (
                <div style={{ color: '#aeb6bf', fontSize: '0.85rem', marginTop: '0.5rem' }}>
                  ජනනය කළේ: {new Date(horoscope.createdAt).toLocaleString('si-LK')}
                </div>
              )}
            </div>
          ) : (
            <p style={{ color: '#aeb6bf', marginBottom: '2rem' }}>
              {getCurrentDate()}
            </p>
          )}
        </div>

        <div className="horoscope-section">
          <div style={{ marginBottom: '1.5rem' }}>
            <h3 className="horoscope-title" style={{ margin: 0 }}>{getUIText('todaysHoroscope')}</h3>
          </div>
          
          {lastUpdated && (
            <div style={{ 
              fontSize: '0.85rem', 
              color: '#aeb6bf', 
              marginBottom: '1rem',
              textAlign: 'center',
              fontStyle: 'italic'
            }}>
              {getUIText('lastUpdated')}: {lastUpdated.toLocaleTimeString('si-LK', {
                hour: '2-digit', 
                minute: '2-digit',
                hour12: true
              })}
            </div>
          )}
          
          {loading && (
            <div className="loading">
              {getUIText('loading')}
            </div>
          )}

          {refreshing && (
            <div className="loading">
              {getUIText('refreshing')}
            </div>
          )}
          
          {error && (
            <div className="error">
              {error}
              <button 
                onClick={handleRefresh}
                style={{
                  marginLeft: '1rem',
                  background: 'rgba(231, 76, 60, 0.1)',
                  border: '1px solid #e74c3c',
                  color: '#e74c3c',
                  padding: '0.4rem 0.8rem',
                  borderRadius: '15px',
                  cursor: 'pointer',
                  fontSize: '0.8rem'
                }}
              >
                {getUIText('refreshHoroscope')}
              </button>
            </div>
          )}
          
          {!loading && !refreshing && !error && horoscope && (
            <StructuredHoroscopeDisplay horoscope={horoscope} />
          )}
        </div>





        <div className="spiritual-message" style={{
          marginTop: '3rem',
          padding: '2rem',
          background: 'rgba(244, 208, 63, 0.1)',
          borderRadius: '15px',
          border: '1px solid rgba(244, 208, 63, 0.3)',
          textAlign: 'center'
        }}>
          <p style={{ color: '#f4d03f', fontStyle: 'italic', fontSize: '1.1rem' }}>
            {getUIText('spiritualMessage')}
          </p>
        </div>
      </div>
    </div>

    {/* Mobile Responsive Styles for Language Selector */}
    <style jsx>{`
      @media (max-width: 768px) {
        .language-selector-container {
          top: 15px !important;
          right: 15px !important;
        }
      }

      @media (max-width: 480px) {
        .language-selector-container {
          top: 10px !important;
          right: 10px !important;
        }
      }

      @media (max-width: 360px) {
        .language-selector-container {
          top: 8px !important;
          right: 8px !important;
        }
      }
    `}</style>
    </TranslationLoader>
  );
};

export default ZodiacPage;
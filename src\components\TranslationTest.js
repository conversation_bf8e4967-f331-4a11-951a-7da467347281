import React, { useState, useEffect } from 'react';
import { useTranslation } from '../contexts/TranslationContext';

const TranslationTest = () => {
  const { currentLanguage, translateText, changeLanguage, isTranslating } = useTranslation();
  const [testResult, setTestResult] = useState('');
  const [error, setError] = useState('');

  const testTranslation = async () => {
    try {
      setError('');
      setTestResult('Testing...');

      console.log('Current language:', currentLanguage);
      console.log('Translation function available:', typeof translateText);

      if (currentLanguage === 'sinhala') {
        setTestResult('Please select English or Tamil to test translation');
        return;
      }

      // Test direct API call first
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
      console.log('API URL:', apiUrl);

      try {
        const response = await fetch(`${apiUrl}/translate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            text: 'ආදරය',
            targetLanguage: currentLanguage,
            targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',
            context: 'test'
          })
        });

        console.log('Direct API response status:', response.status);
        const data = await response.json();
        console.log('Direct API response data:', data);

        if (data.success) {
          setTestResult(`Direct API: ${data.translatedText}`);
        } else {
          setError(`Direct API Error: ${data.error}`);
        }
      } catch (apiError) {
        console.error('Direct API error:', apiError);
        setError(`Direct API Error: ${apiError.message}`);
      }

      // Also test through translation context
      try {
        const result = await translateText('ආදරය', currentLanguage, 'test');
        console.log('Context translation result:', result);
        setTestResult(prev => prev + ` | Context: ${result}`);
      } catch (contextError) {
        console.error('Context translation error:', contextError);
        setError(prev => prev + ` | Context Error: ${contextError.message}`);
      }

    } catch (err) {
      console.error('Translation test error:', err);
      setError(`Error: ${err.message}`);
    }
  };

  useEffect(() => {
    console.log('TranslationTest component mounted');
    console.log('Current language:', currentLanguage);
    console.log('Is translating:', isTranslating);
  }, [currentLanguage, isTranslating]);

  return (
    <div style={{
      position: 'fixed',
      top: '100px',
      left: '20px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '20px',
      borderRadius: '10px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h3>Translation Test</h3>
      <p>Current Language: {currentLanguage}</p>
      <p>Is Translating: {isTranslating ? 'Yes' : 'No'}</p>
      
      <div style={{ marginBottom: '10px' }}>
        <button onClick={() => changeLanguage('sinhala')} style={{ margin: '5px' }}>
          Sinhala
        </button>
        <button onClick={() => changeLanguage('english')} style={{ margin: '5px' }}>
          English
        </button>
        <button onClick={() => changeLanguage('tamil')} style={{ margin: '5px' }}>
          Tamil
        </button>
      </div>
      
      <button onClick={testTranslation} disabled={isTranslating}>
        Test Translation
      </button>
      
      {testResult && (
        <div style={{ marginTop: '10px', color: 'lightgreen' }}>
          {testResult}
        </div>
      )}
      
      {error && (
        <div style={{ marginTop: '10px', color: 'red' }}>
          {error}
        </div>
      )}
    </div>
  );
};

export default TranslationTest;

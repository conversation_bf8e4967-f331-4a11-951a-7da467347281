[{"C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js": "3", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js": "4", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js": "6", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js": "7", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js": "8", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js": "9", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js": "10", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js": "11", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js": "12", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js": "13", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js": "14", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\contexts\\TranslationContext.js": "15", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LanguageSelector.js": "16", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\TranslationLoader.js": "17", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useTranslatedContent.js": "18", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\TranslationService.js": "19", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\TranslationTest.js": "20"}, {"size": 253, "mtime": 1750790409543, "results": "21", "hashOfConfig": "22"}, {"size": 4173, "mtime": 1751811482927, "results": "23", "hashOfConfig": "22"}, {"size": 8069, "mtime": 1751564462690, "results": "24", "hashOfConfig": "22"}, {"size": 8332, "mtime": 1751565228966, "results": "25", "hashOfConfig": "22"}, {"size": 17108, "mtime": 1751812246010, "results": "26", "hashOfConfig": "22"}, {"size": 29782, "mtime": 1751810744987, "results": "27", "hashOfConfig": "22"}, {"size": 8284, "mtime": 1751565326750, "results": "28", "hashOfConfig": "22"}, {"size": 3837, "mtime": 1750614946000, "results": "29", "hashOfConfig": "22"}, {"size": 2802, "mtime": 1750614946000, "results": "30", "hashOfConfig": "22"}, {"size": 7496, "mtime": 1750614946000, "results": "31", "hashOfConfig": "22"}, {"size": 7302, "mtime": 1751564707800, "results": "32", "hashOfConfig": "22"}, {"size": 6127, "mtime": 1751389760889, "results": "33", "hashOfConfig": "22"}, {"size": 6410, "mtime": 1751565137421, "results": "34", "hashOfConfig": "22"}, {"size": 13813, "mtime": 1751565044144, "results": "35", "hashOfConfig": "22"}, {"size": 3419, "mtime": 1751809259878, "results": "36", "hashOfConfig": "22"}, {"size": 7365, "mtime": 1751810654299, "results": "37", "hashOfConfig": "22"}, {"size": 4023, "mtime": 1751810683304, "results": "38", "hashOfConfig": "22"}, {"size": 6699, "mtime": 1751810422051, "results": "39", "hashOfConfig": "22"}, {"size": 5850, "mtime": 1751811886287, "results": "40", "hashOfConfig": "22"}, {"size": 4385, "mtime": 1751811989041, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cwcvdy", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js", ["102"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js", ["103"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js", ["104", "105", "106"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js", ["107"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\contexts\\TranslationContext.js", ["108"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LanguageSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\TranslationLoader.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useTranslatedContent.js", ["109", "110"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\TranslationService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\TranslationTest.js", [], [], {"ruleId": "111", "severity": 1, "message": "112", "line": 8, "column": 10, "nodeType": "113", "messageId": "114", "endLine": 8, "endColumn": 22}, {"ruleId": "111", "severity": 1, "message": "115", "line": 9, "column": 3, "nodeType": "113", "messageId": "114", "endLine": 9, "endColumn": 16}, {"ruleId": "111", "severity": 1, "message": "116", "line": 365, "column": 47, "nodeType": "113", "messageId": "114", "endLine": 365, "endColumn": 65}, {"ruleId": "117", "severity": 1, "message": "118", "line": 380, "column": 6, "nodeType": "119", "endLine": 380, "endColumn": 42, "suggestions": "120"}, {"ruleId": "117", "severity": 1, "message": "121", "line": 543, "column": 6, "nodeType": "119", "endLine": 543, "endColumn": 15, "suggestions": "122"}, {"ruleId": "123", "severity": 1, "message": "124", "line": 230, "column": 1, "nodeType": "125", "endLine": 248, "endColumn": 3}, {"ruleId": "117", "severity": 1, "message": "126", "line": 97, "column": 6, "nodeType": "119", "endLine": 97, "endColumn": 8, "suggestions": "127"}, {"ruleId": "111", "severity": 1, "message": "128", "line": 6, "column": 43, "nodeType": "113", "messageId": "114", "endLine": 6, "endColumn": 60}, {"ruleId": "111", "severity": 1, "message": "129", "line": 112, "column": 11, "nodeType": "113", "messageId": "114", "endLine": 112, "endColumn": 28}, "no-unused-vars", "'useAnalytics' is defined but never used.", "Identifier", "unusedVar", "'CUSTOM_EVENTS' is defined but never used.", "'translationLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", "ArrayExpression", ["130"], "React Hook useCallback has a missing dependency: 'analytics'. Either include it or remove the dependency array.", ["131"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useCallback has a missing dependency: 'languages'. Either include it or remove the dependency array.", ["132"], "'translateMultiple' is assigned a value but never used.", "'getTranslatedText' is assigned a value but never used.", {"desc": "133", "fix": "134"}, {"desc": "135", "fix": "136"}, {"desc": "137", "fix": "138"}, "Update the dependencies array to be: [horoscope, getTranslatedCategories, categories]", {"range": "139", "text": "140"}, "Update the dependencies array to be: [analytics, sign.id]", {"range": "141", "text": "142"}, "Update the dependencies array to be: [languages]", {"range": "143", "text": "144"}, [12367, 12403], "[horoscope, getTranslatedCategories, categories]", [17769, 17778], "[analytics, sign.id]", [2771, 2773], "[languages]"]
[{"C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js": "3", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js": "4", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js": "6", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js": "7", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js": "8", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js": "9", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js": "10", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js": "11", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js": "12", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js": "13", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js": "14", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\contexts\\TranslationContext.js": "15", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LanguageSelector.js": "16", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\TranslationLoader.js": "17", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useTranslatedContent.js": "18", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\TranslationService.js": "19"}, {"size": 253, "mtime": 1750790409543, "results": "20", "hashOfConfig": "21"}, {"size": 4031, "mtime": 1751810627368, "results": "22", "hashOfConfig": "21"}, {"size": 8069, "mtime": 1751564462690, "results": "23", "hashOfConfig": "21"}, {"size": 8332, "mtime": 1751565228966, "results": "24", "hashOfConfig": "21"}, {"size": 19009, "mtime": 1751810786143, "results": "25", "hashOfConfig": "21"}, {"size": 29782, "mtime": 1751810744987, "results": "26", "hashOfConfig": "21"}, {"size": 8284, "mtime": 1751565326750, "results": "27", "hashOfConfig": "21"}, {"size": 3837, "mtime": 1750614946000, "results": "28", "hashOfConfig": "21"}, {"size": 2802, "mtime": 1750614946000, "results": "29", "hashOfConfig": "21"}, {"size": 7496, "mtime": 1750614946000, "results": "30", "hashOfConfig": "21"}, {"size": 7302, "mtime": 1751564707800, "results": "31", "hashOfConfig": "21"}, {"size": 6127, "mtime": 1751389760889, "results": "32", "hashOfConfig": "21"}, {"size": 6410, "mtime": 1751565137421, "results": "33", "hashOfConfig": "21"}, {"size": 13813, "mtime": 1751565044144, "results": "34", "hashOfConfig": "21"}, {"size": 3419, "mtime": 1751809259878, "results": "35", "hashOfConfig": "21"}, {"size": 7365, "mtime": 1751810654299, "results": "36", "hashOfConfig": "21"}, {"size": 4023, "mtime": 1751810683304, "results": "37", "hashOfConfig": "21"}, {"size": 6699, "mtime": 1751810422051, "results": "38", "hashOfConfig": "21"}, {"size": 5655, "mtime": 1751810700321, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cwcvdy", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js", ["97"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js", ["98"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js", ["99", "100", "101"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js", ["102"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\contexts\\TranslationContext.js", ["103"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LanguageSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\TranslationLoader.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useTranslatedContent.js", ["104", "105"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\TranslationService.js", [], [], {"ruleId": "106", "severity": 1, "message": "107", "line": 7, "column": 10, "nodeType": "108", "messageId": "109", "endLine": 7, "endColumn": 22}, {"ruleId": "106", "severity": 1, "message": "110", "line": 9, "column": 3, "nodeType": "108", "messageId": "109", "endLine": 9, "endColumn": 16}, {"ruleId": "106", "severity": 1, "message": "111", "line": 365, "column": 47, "nodeType": "108", "messageId": "109", "endLine": 365, "endColumn": 65}, {"ruleId": "112", "severity": 1, "message": "113", "line": 380, "column": 6, "nodeType": "114", "endLine": 380, "endColumn": 42, "suggestions": "115"}, {"ruleId": "112", "severity": 1, "message": "116", "line": 543, "column": 6, "nodeType": "114", "endLine": 543, "endColumn": 15, "suggestions": "117"}, {"ruleId": "118", "severity": 1, "message": "119", "line": 230, "column": 1, "nodeType": "120", "endLine": 248, "endColumn": 3}, {"ruleId": "112", "severity": 1, "message": "121", "line": 97, "column": 6, "nodeType": "114", "endLine": 97, "endColumn": 8, "suggestions": "122"}, {"ruleId": "106", "severity": 1, "message": "123", "line": 6, "column": 43, "nodeType": "108", "messageId": "109", "endLine": 6, "endColumn": 60}, {"ruleId": "106", "severity": 1, "message": "124", "line": 112, "column": 11, "nodeType": "108", "messageId": "109", "endLine": 112, "endColumn": 28}, "no-unused-vars", "'useAnalytics' is defined but never used.", "Identifier", "unusedVar", "'CUSTOM_EVENTS' is defined but never used.", "'translationLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", "ArrayExpression", ["125"], "React Hook useCallback has a missing dependency: 'analytics'. Either include it or remove the dependency array.", ["126"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useCallback has a missing dependency: 'languages'. Either include it or remove the dependency array.", ["127"], "'translateMultiple' is assigned a value but never used.", "'getTranslatedText' is assigned a value but never used.", {"desc": "128", "fix": "129"}, {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, "Update the dependencies array to be: [horoscope, getTranslatedCategories, categories]", {"range": "134", "text": "135"}, "Update the dependencies array to be: [analytics, sign.id]", {"range": "136", "text": "137"}, "Update the dependencies array to be: [languages]", {"range": "138", "text": "139"}, [12367, 12403], "[horoscope, getTranslatedCategories, categories]", [17769, 17778], "[analytics, sign.id]", [2771, 2773], "[languages]"]